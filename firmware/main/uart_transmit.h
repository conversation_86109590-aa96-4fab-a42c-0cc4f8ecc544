#ifndef UART_TRANSMIT_H
#define UART_TRANSMIT_H

#include <stdint.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "adxl345.h"

/**
 * @brief Initialize UART0 for high-performance data transmission
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_init(void);

/**
 * @brief Create UART transmission task
 * @param sample_queue Queue handle to read packed samples from
 * @param task_name Name for the task
 * @param stack_size Stack size for the task
 * @param priority Task priority
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_create_task(QueueHandle_t sample_queue, const char *task_name, uint32_t stack_size, UBaseType_t priority);

/**
 * @brief Deinitialize UART transmission
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_deinit(void);

#endif // UART_TRANSMIT_H
