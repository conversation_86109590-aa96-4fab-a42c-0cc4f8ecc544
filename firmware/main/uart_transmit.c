#include "uart_transmit.h"
#include "interface.h"
#include "adxl345.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <string.h>

#define TAG "UART_TX"
#define UART_PORT_NUM UART_NUM_0
#define UART_BUF_SIZE (2048)
#define UART_TX_PIN 43
#define UART_RX_PIN 44

/**
 * @brief Parameters passed to the UART transmission task
 */
typedef struct
{
    QueueHandle_t sample_queue; ///< Queue to read packed samples from
} uart_transmit_task_params_t;

/**
 * @brief Initialize UART0 for high-performance data transmission
 */
esp_err_t uart_transmit_init(void)
{
    ESP_LOGI(TAG, "Initializing UART0 for data transmission at %d baud", UART_BAUD_RATE);

    // Configure UART parameters
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // Install UART driver
    esp_err_t ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure UART parameters
    ret = uart_param_config(UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    // Set UART pins (TX, RX, RTS, CTS)
    ret = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    ESP_LOGI(TAG, "UART0 initialized successfully (TX=%d, RX=%d, Baud=%d)",
             UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);
    return ESP_OK;
}

/**
 * @brief UART transmission task - reads packed samples from queue and transmits them
 */
static void uart_transmit_task(void *pvParameters)
{
    uart_transmit_task_params_t *params = (uart_transmit_task_params_t *)pvParameters;
    QueueHandle_t sample_queue = params->sample_queue;

    adxl345_sample_t sample;
    uint32_t sample_count = 0;

    ESP_LOGI(TAG, "UART transmission task started");

    while (1)
    {
        // Block until a sample is available in the queue
        if (xQueueReceive(sample_queue, &sample, portMAX_DELAY) == pdTRUE)
        {
            // Transmit the packed sample directly (5 bytes)
            uart_write_bytes(UART_PORT_NUM, sample.data, sizeof(sample.data));
            sample_count++;
            if (sample_count %  == 0)
            {
                // write synchronization packet
                uart_write_bytes(UART_PORT_NUM, SYNC_PACKET, 5);
            }
        }
    }
}

/**
 * @brief Create UART transmission task
 */
esp_err_t uart_transmit_create_task(QueueHandle_t sample_queue, const char *task_name, uint32_t stack_size, UBaseType_t priority)
{
    if (sample_queue == NULL)
    {
        ESP_LOGE(TAG, "Sample queue cannot be NULL");
        return ESP_ERR_INVALID_ARG;
    }

    // Allocate memory for task parameters
    uart_transmit_task_params_t *params = malloc(sizeof(uart_transmit_task_params_t));
    if (params == NULL)
    {
        ESP_LOGE(TAG, "Failed to allocate memory for task parameters");
        return ESP_ERR_NO_MEM;
    }

    // Initialize task parameters
    params->sample_queue = sample_queue;

    BaseType_t xReturned = xTaskCreate(
        uart_transmit_task, // Task function
        task_name,          // Name for debugging
        stack_size,         // Stack size
        params,             // Parameter = pointer to params struct
        priority,           // Priority
        NULL);

    if (xReturned != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create UART transmission task: %s", task_name);
        free(params); // Clean up allocated memory
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "UART transmission task created: %s", task_name);
    return ESP_OK;
}

/**
 * @brief Deinitialize UART transmission
 */
esp_err_t uart_transmit_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing UART transmission");
    return uart_driver_delete(UART_PORT_NUM);
}
