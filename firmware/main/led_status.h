/**
 * @file led_status.h
 * @brief LED status indication for ESP32-S3-DevKitC-1 v1.1
 * 
 * Controls the onboard RGB LED (WS2812) on GPIO38 to indicate system status:
 * - Red: Initializing
 * - White: Two sensors detected
 * - Green: Only sensor 1 detected  
 * - Blue: Only sensor 2 detected
 */

#pragma once

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief LED status states
 */
typedef enum {
    LED_STATUS_INITIALIZING,    ///< Red - system initializing
    LED_STATUS_TWO_SENSORS,     ///< White - both sensors detected
    LED_STATUS_SENSOR1_ONLY,    ///< Green - only sensor 1 detected
    LED_STATUS_SENSOR2_ONLY,    ///< Blue - only sensor 2 detected
    LED_STATUS_OFF              ///< LED off
} led_status_t;

/**
 * @brief Initialize the LED status system
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t led_status_init(void);

/**
 * @brief Set the LED status
 * @param status The status to display
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t led_status_set(led_status_t status);

/**
 * @brief Deinitialize the LED status system
 */
void led_status_deinit(void);

#ifdef __cplusplus
}
#endif
