use anyhow::{Context, Result};
use egui_plot::PlotPoint;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::io::{AsyncRead, AsyncReadExt};
use tokio::sync::{mpsc, Mutex};

use crate::csv_logger::CsvLogger;
use crate::gui::{LabrecordApp, SensorData};
use crate::interface::{SAMPLES_PER_SECOND, SYNC_INTERVAL};

use crate::sample::Sample;
use crate::{wire, RUNNING};

// Constants
//const TIME_PER_SAMPLE: Duration = Duration::from_micros(1_000_000 / SAMPLES_PER_SECOND as u64);

/// Debug function to print packet data in hex and ASCII
pub fn data_print(packet: &[u8]) {
    for chunk in packet.chunks(48) {
        // print packet in hex and ascii, with hex and ascii perfectly aligned (one line each)
        for byte in chunk.iter() {
            print!("{:02X} ", byte);
        }
        println!();
        for byte in chunk.iter() {
            print!(
                " {} ",
                if byte.is_ascii_graphic() {
                    *byte as char
                } else {
                    '.'
                }
            );
        }
        println!();
    }
}

/// Add samples to sensor data for GUI visualization and FFT analysis
pub async fn add_samples(sensor: &Arc<Mutex<SensorData>>, time_offset: f64, sample: &Sample) {
    let mut sensor = sensor.lock().await;
    sensor.x.push(PlotPoint {
        x: time_offset,
        y: sample.x as f64,
    });
    sensor.y.push(PlotPoint {
        x: time_offset,
        y: sample.y as f64,
    });
    sensor.z.push(PlotPoint {
        x: time_offset,
        y: sample.z as f64,
    });

    // Add sample to FFT buffer
    sensor.fft_buffer.add_sample(sample);

    // Perform FFT analysis if buffer is ready
    if sensor.fft_buffer.is_ready()
        && sensor.last_analysis_time.elapsed() > Duration::from_millis(500)
    {
        sensor.last_analysis_time = Instant::now();
        if let Some(fft_result) = sensor.fft_buffer.analyze() {
            // Update frequency domain plots for all three axes (up to 200 Hz)

            // Clear all frequency data
            sensor.fft_frequencies_x.clear();
            sensor.fft_frequencies_y.clear();
            sensor.fft_frequencies_z.clear();

            // Populate X-axis frequency data
            for (&freq, &magnitude) in fft_result
                .x
                .frequencies
                .iter()
                .zip(fft_result.x.magnitudes.iter())
            {
                if freq >= 1.0 && freq <= SAMPLES_PER_SECOND as f64 / 2.0 {
                    sensor.fft_frequencies_x.push(PlotPoint {
                        x: freq,
                        y: magnitude,
                    });
                }
            }

            // Populate Y-axis frequency data
            for (&freq, &magnitude) in fft_result
                .y
                .frequencies
                .iter()
                .zip(fft_result.y.magnitudes.iter())
            {
                if freq >= 1.0 && freq <= SAMPLES_PER_SECOND as f64 / 2.0 {
                    sensor.fft_frequencies_y.push(PlotPoint {
                        x: freq,
                        y: magnitude,
                    });
                }
            }

            // Populate Z-axis frequency data
            for (&freq, &magnitude) in fft_result
                .z
                .frequencies
                .iter()
                .zip(fft_result.z.magnitudes.iter())
            {
                if freq >= 1.0 && freq <= SAMPLES_PER_SECOND as f64 / 2.0 {
                    sensor.fft_frequencies_z.push(PlotPoint {
                        x: freq,
                        y: magnitude,
                    });
                }
            }

            sensor.fft_result = Some(fft_result);
        }
    }
}

/// Synchronize to sample boundary by finding sync packet and skipping to correct position
pub async fn synchronize_to_sample_boundary(
    stream: &mut Box<dyn AsyncRead + Unpin + Send>,
) -> Result<()> {
    println!("Synchronizing to sample boundary...");

    // The sync packet is 5 bytes: "\0\0\n\0\0"
    let sync_pattern = [0u8, 0u8, b'\n', 0u8, 0u8];

    // Read a large chunk that definitely contains at least one sync cycle
    let chunk_size = (SYNC_INTERVAL as usize * 5) + 5 + 1000; // Extra safety margin
    let mut buffer = vec![0u8; chunk_size];

    stream.read_exact(&mut buffer).await?;

    // Find the first sync pattern
    let first_sync_pos = buffer
        .windows(5)
        .position(|w| w == sync_pattern)
        .ok_or_else(|| anyhow::anyhow!("No sync pattern found in chunk"))?;

    println!("Found sync pattern at position {}", first_sync_pos);

    // Calculate where the next sync should be
    let expected_next_sync = first_sync_pos + (SYNC_INTERVAL as usize * 5) + 5;

    // Verify the next sync pattern is where expected
    if expected_next_sync + 5 <= buffer.len() {
        let next_sync_slice = &buffer[expected_next_sync..expected_next_sync + 5];
        if next_sync_slice != sync_pattern {
            anyhow::bail!("Next sync pattern not found at expected position");
        }
        println!(
            "Verified next sync pattern at position {}",
            expected_next_sync
        );
    }

    // Now we need to skip to the position right after the first sync
    // We've already read chunk_size bytes, so we need to skip back to the right position
    let bytes_to_skip_back = buffer.len() - (first_sync_pos + 5);

    // Read and discard the bytes we need to skip
    if bytes_to_skip_back > 0 {
        let mut discard_buffer = vec![0u8; bytes_to_skip_back];
        stream.read_exact(&mut discard_buffer).await?;
    }

    println!("Synchronized to sample boundary");
    Ok(())
}

/// Data structure for individual processed samples sent between UART collection and processing tasks
#[derive(Debug, Clone)]
pub struct ProcessedSample {
    pub sensor_id: u8,
    pub sample: Sample,
    pub time_offset: f64,
    pub unix_time_us: u128,
}

/// UART collection task - reads 5-byte samples from UART, processes them, and sends individual samples over a channel
pub async fn uart_collection_task(
    mut stream: Box<dyn AsyncRead + Unpin + Send>,
    sample_sender: mpsc::Sender<ProcessedSample>,
) -> Result<()> {
    println!("Starting UART collection task...");

    let batch_start = Instant::now();

    // Synchronize to sample boundary by finding sync packet
    for i in 0..10 {
        println!("Synchronizing to sample boundary... (try {})", i);
        if let Err(e) = synchronize_to_sample_boundary(&mut stream).await {
            println!("Failed to synchronize: {}", e);
            if i == 9 {
                anyhow::bail!("Failed to synchronize after 10 tries");
            }
        } else {
            break;
        }
    }

    // Process samples for one hour (approximately)
    let samples_per_hour = SAMPLES_PER_SECOND as usize * 60 * 60 * 2; // Both sensors combined
    let mut num_samples = 0usize;
    let mut samples_since_sync = 0u32;

    loop {
        if !RUNNING.load(std::sync::atomic::Ordering::SeqCst) {
            break;
        }
        if num_samples >= samples_per_hour {
            println!("Collected {} samples (1h), rotating files...", num_samples);
            break;
        }

        // Check if we should expect a sync packet
        if samples_since_sync >= SYNC_INTERVAL {
            // Read and verify sync packet
            let mut sync_buffer = [0u8; 5];
            stream
                .read_exact(&mut sync_buffer)
                .await
                .context("Failed to read expected sync packet")?;

            let sync_pattern = [0u8, 0u8, b'\n', 0u8, 0u8];
            if sync_buffer != sync_pattern {
                println!(
                    "Warning: Expected sync packet not found after {} samples",
                    samples_since_sync
                );
                data_print(&sync_buffer);
                // Try to resynchronize
                break;
            }
            println!(
                "Received expected sync packet after {} samples",
                samples_since_sync
            );
            samples_since_sync = 0;
            continue;
        }

        // Read one complete 5-byte sample
        let mut sample_buffer = [0u8; 5];
        stream
            .read_exact(&mut sample_buffer)
            .await
            .context("Failed to read sample")?;

        samples_since_sync += 1;

        // Parse the sample using the wire module
        let (sensor_id, sample) = match wire::unpack_sample(&sample_buffer) {
            Ok(parsed) => parsed,
            Err(e) => {
                println!("Failed to parse sample: {}", e);
                data_print(&sample_buffer);
                // Try to resynchronize
                break;
            }
        };

        num_samples += 1;

        // Calculate timing information for this sample
        let now = Instant::now();
        let now_sys = SystemTime::now();
        let time_offset = now.saturating_duration_since(batch_start);
        let unix_time_us = now_sys
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_micros();

        // Create processed sample
        let processed_sample = ProcessedSample {
            sensor_id,
            sample,
            time_offset: time_offset.as_secs_f64(),
            unix_time_us,
        };

        // Send processed sample to processing task
        if let Err(_) = sample_sender.send(processed_sample).await {
            println!("Data processing task has stopped, ending UART collection");
            break;
        }

        // Print progress occasionally
        if num_samples % 50_000 == 0 {
            println!("Collected {} samples", num_samples);
        }
    }

    println!("UART collection task finished");
    Ok(())
}

/// Data processing task - consumes individual processed samples from channel and handles GUI/CSV logging
pub async fn data_processing_task(
    mut sample_receiver: mpsc::Receiver<ProcessedSample>,
    csv_logger1: CsvLogger,
    csv_logger2: CsvLogger,
    gui: Option<LabrecordApp>,
) -> Result<()> {
    println!("Starting data processing task...");

    let mut processed_samples = 0usize;
    let mut buffer = String::new();

    while let Some(processed_sample) = sample_receiver.recv().await {
        if !RUNNING.load(std::sync::atomic::Ordering::SeqCst) {
            break;
        }

        let ProcessedSample {
            sensor_id,
            sample,
            time_offset,
            unix_time_us,
        } = processed_sample;

        processed_samples += 1;

        // Process sample based on sensor ID
        match sensor_id {
            0 => {
                // Sensor 1 (sensor_id 0 in firmware = sensor 1)
                // Update GUI if available
                if let Some(ref gui) = gui {
                    add_samples(&gui.sensor1, time_offset, &sample).await;
                }

                // Log to CSV
                csv_logger1
                    .log(&mut buffer, unix_time_us, &sample)
                    .await
                    .context("Failed to log sensor 1 sample")?;
            }
            1 => {
                // Sensor 2 (sensor_id 1 in firmware = sensor 2)
                // Update GUI if available
                if let Some(ref gui) = gui {
                    add_samples(&gui.sensor2, time_offset, &sample).await;
                }

                // Log to CSV
                csv_logger2
                    .log(&mut buffer, unix_time_us, &sample)
                    .await
                    .context("Failed to log sensor 2 sample")?;
            }
            _ => {
                println!("Warning: Invalid sensor ID: {}", sensor_id);
                continue;
            }
        }

        // Print progress occasionally
        if processed_samples % 50_000 == 0 {
            println!("Processed {} samples", processed_samples);
        }
    }

    // Flush CSV loggers before finishing
    let _ = csv_logger1.flush().await;
    let _ = csv_logger2.flush().await;

    println!("Data processing task finished");
    Ok(())
}

/// Process a batch of samples for data collection using separated tasks
pub async fn do_batch(
    stream: Box<dyn AsyncRead + Unpin + Send>,
    csv_logger1: CsvLogger,
    csv_logger2: CsvLogger,
    gui: Option<&LabrecordApp>,
) -> Result<()> {
    println!("Starting batch processing with separated tasks...");

    // Create channel for communication between UART collection and data processing tasks
    let (sample_sender, sample_receiver) = mpsc::channel::<ProcessedSample>(10000); // Larger buffer for individual samples

    // Clone GUI for the data processing task
    let gui_clone = gui.cloned();

    // Create futures without spawning tasks
    let uart_future = uart_collection_task(stream, sample_sender);
    let processing_future = data_processing_task(
        sample_receiver,
        csv_logger1.clone(),
        csv_logger2.clone(),
        gui_clone,
    );

    // Use select to return as soon as either future completes (enabling fast retry on UART failure)
    let res = tokio::select! {
        uart_result = uart_future => {
            match uart_result {
                Ok(()) => {
                    println!("UART collection completed successfully");
                    Ok(())
                }
                Err(e) => {
                    println!("UART collection failed: {}", e);
                    Err(e)
                }
            }
        }
        processing_result = processing_future => {
            match processing_result {
                Ok(()) => {
                    println!("Data processing completed successfully");
                    Ok(())
                }
                Err(e) => {
                    println!("Data processing failed: {}", e);
                    Err(e)
                }
            }
        }
    };

    let _ = csv_logger1.flush().await;
    let _ = csv_logger2.flush().await;

    res
}
