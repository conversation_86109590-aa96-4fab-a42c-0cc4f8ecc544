use anyhow::Result;

use crate::sample::Sample;

/// Unpack a 5-byte sample into sensor ID and accelerometer data
pub fn unpack_sample(sample_data: &[u8; 5]) -> Result<(u8, Sample)> {
    // Reconstruct the 40-bit packed value (little-endian)
    let mut packed: u64 = 0;
    packed |= sample_data[0] as u64;
    packed |= (sample_data[1] as u64) << 8;
    packed |= (sample_data[2] as u64) << 16;
    packed |= (sample_data[3] as u64) << 24;
    packed |= (sample_data[4] as u64) << 32;

    // Extract fields
    let sensor_id = (packed & 0x01) as u8; // Bit 0
    let x13 = ((packed >> 1) & 0x1FFF) as u16; // Bits 1-13
    let y13 = ((packed >> 14) & 0x1FFF) as u16; // Bits 14-26
    let z13 = ((packed >> 27) & 0x1FFF) as u16; // Bits 27-39

    // Convert 13-bit values back to 16-bit signed values with proper sign extension
    let x = sign_extend_13_to_16(x13);
    let y = sign_extend_13_to_16(y13);
    let z = sign_extend_13_to_16(z13);

    let sample = Sample { x, y, z };

    Ok((sensor_id, sample))
}

/// Convert 13-bit two's complement value to 16-bit signed value with proper sign extension
fn sign_extend_13_to_16(value_13bit: u16) -> i16 {
    // Check if the sign bit (bit 12) is set
    if (value_13bit & 0x1000) != 0 {
        // Negative number: set bits 13-15 to 1 for sign extension
        (value_13bit | 0xE000) as i16
    } else {
        // Positive number: bits 13-15 are already 0
        value_13bit as i16
    }
}

/// Parse a raw adxl345_sample_t structure into sensor ID and Sample
pub fn parse_adxl345_sample(sample: &[u8; 5]) -> Result<(u8, Sample)> {
    unpack_sample(sample)
}
