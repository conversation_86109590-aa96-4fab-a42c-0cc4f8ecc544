use std::env;

fn main() {
    println!("cargo:rerun-if-changed=firmware/main/packet.h");

    let bindings = bindgen::Builder::default()
        .header("../firmware/main/interface.h")
        .allowlist_var("UART_BAUD_RATE")
        .allowlist_var("SAMPLES_PER_SECOND")
        .allowlist_var("SYNC_INTERVAL")
        .allowlist_var("SYNC_PACKET")
        .generate()
        .expect("Unable to generate bindings");

    let out_path = env::var("OUT_DIR").unwrap();
    bindings
        .write_to_file(out_path + "/bindings.rs")
        .expect("Unable to write bindings to file");
}
