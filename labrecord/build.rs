use std::env;

fn main() {
    println!("cargo:rerun-if-changed=firmware/main/packet.h");

    let bindings = bindgen::Builder::default()
        .header("../firmware/main/interface.h")
        .allowlist_type("adxl345_sample_t")
        .allowlist_var("UART_BAUD_RATE")
        .allowlist_var("SAMPLES_PER_SECOND")
        .generate()
        .expect("Unable to generate bindings");

    let out_path = env::var("OUT_DIR").unwrap();
    bindings
        .write_to_file(out_path + "/bindings.rs")
        .expect("Unable to write bindings to file");
}
